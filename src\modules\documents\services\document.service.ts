import { Injectable, Logger } from '@nestjs/common';
import { DocumentRepository } from '../repositories/document.repository';
import { DocumentFolderRepository } from '../repositories/document-folder.repository';
import { DocumentAccessLogRepository } from '../repositories/document-access-log.repository';
import {
  CreateDocumentDto,
  UpdateDocumentDto,
  UploadDocumentDto,
  UploadDocumentResponseDto,
  DocumentQueryDto,
  DocumentSearchDto,
  DocumentResponseDto,
  DocumentWithDownloadUrlResponseDto,
} from '../dto/document';
import { Document } from '../entities/document.entity';
import { DocumentType, ProcessingStatus } from '../enums';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { DOCUMENTS_ERROR_CODES } from '../errors';

/**
 * Service xử lý logic nghiệp vụ cho tài liệu
 */
@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);

  constructor(
    private readonly documentRepository: DocumentRepository,
    private readonly documentFolderRepository: DocumentFolderRepository,
    private readonly documentAccessLogRepository: DocumentAccessLogRepository,
  ) {}

  /**
   * Tạo tài liệu mới từ dữ liệu S3
   * @param tenantId ID tenant
   * @param createDto Dữ liệu tạo tài liệu
   * @param createdBy ID người tạo
   * @returns Tài liệu đã tạo
   */
  async create(
    tenantId: number,
    createDto: CreateDocumentDto,
    createdBy: number,
  ): Promise<DocumentResponseDto> {
    this.logger.log(`Tạo tài liệu mới: ${createDto.title} cho tenant ${tenantId}`);

    // Kiểm tra thư mục nếu có
    if (createDto.folderId) {
      const folder = await this.documentFolderRepository.findById(tenantId, createDto.folderId);
      if (!folder) {
        throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_NOT_FOUND);
      }
      if (!folder.isActive) {
        throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_INACTIVE);
      }
    }

    // Kiểm tra trùng lặp S3 key
    const existingDocument = await this.documentRepository.findByS3Key(tenantId, createDto.s3Key);
    if (existingDocument) {
      throw new AppException(DOCUMENTS_ERROR_CODES.DOCUMENT_S3_KEY_EXISTS);
    }

    // Kiểm tra trùng lặp content hash nếu có
    if (createDto.contentHash) {
      const duplicateDocument = await this.documentRepository.findByContentHash(tenantId, createDto.contentHash);
      if (duplicateDocument) {
        this.logger.warn(`Phát hiện tài liệu trùng lặp với hash: ${createDto.contentHash}`);
        // Có thể throw exception hoặc return existing document tùy business logic
      }
    }

    // Tạo tài liệu
    const documentData: Partial<Document> = {
      ...createDto,
      processingStatus: ProcessingStatus.PENDING,
      createdBy,
      updatedBy: createdBy,
    };

    const document = await this.documentRepository.create(tenantId, documentData);
    
    this.logger.log(`Đã tạo tài liệu ID: ${document.id}`);

    // Log truy cập
    await this.logAccess(tenantId, document.id, createdBy, 'edit', {
      success: true,
      metadata: { action: 'create_document' },
    });

    return this.mapToResponseDto(document);
  }

  /**
   * Upload tài liệu mới (multipart/form-data)
   * @param tenantId ID tenant
   * @param uploadDto Dữ liệu upload
   * @param file File đã upload
   * @param createdBy ID người upload
   * @returns Response upload
   */
  async upload(
    tenantId: number,
    uploadDto: UploadDocumentDto,
    file: any, // Express.Multer.File type issue
    createdBy: number,
  ): Promise<UploadDocumentResponseDto> {
    this.logger.log(`Upload tài liệu: ${uploadDto.title} cho tenant ${tenantId}`);

    // Parse JSON strings nếu có
    let tags: string[] | undefined;
    let metadata: Record<string, any> | undefined;

    if (uploadDto.tags) {
      try {
        tags = JSON.parse(uploadDto.tags);
      } catch (error) {
        throw new AppException(DOCUMENTS_ERROR_CODES.INVALID_TAGS_FORMAT);
      }
    }

    if (uploadDto.metadata) {
      try {
        metadata = JSON.parse(uploadDto.metadata);
      } catch (error) {
        throw new AppException(DOCUMENTS_ERROR_CODES.INVALID_METADATA_FORMAT);
      }
    }

    // TODO: Upload file lên S3 và lấy S3 key
    // const s3Result = await this.s3Service.uploadFile(file, tenantId);
    const s3Key = `documents/tenant-${tenantId}/${Date.now()}-${file.originalname}`;

    // Tạo document từ upload data
    const createDto: CreateDocumentDto = {
      title: uploadDto.title,
      description: uploadDto.description,
      documentType: uploadDto.documentType,
      fileName: file.originalname,
      fileSize: file.size,
      mimeType: file.mimetype,
      folderId: uploadDto.folderId,
      s3Key,
      // s3Bucket: s3Result.bucket,
      // s3Region: s3Result.region,
      // s3Etag: s3Result.etag,
      isPublic: uploadDto.isPublic,
      tags,
      metadata,
    };

    const document = await this.create(tenantId, createDto, createdBy);

    // TODO: Trigger background processing cho OpenAI
    // await this.queueService.addDocumentProcessingJob(document.id);

    return {
      id: document.id,
      title: document.title,
      fileName: document.fileName,
      fileSize: document.fileSize,
      mimeType: document.mimeType,
      s3Key: document.s3Key,
      processingStatus: document.processingStatus,
      createdAt: document.createdAt,
      // downloadUrl: await this.s3Service.getPresignedUrl(document.s3Key),
    };
  }

  /**
   * Lấy danh sách tài liệu với phân trang
   * @param tenantId ID tenant
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tài liệu đã phân trang
   */
  async findAll(
    tenantId: number,
    queryDto: DocumentQueryDto,
  ): Promise<PaginatedResult<DocumentResponseDto>> {
    this.logger.log(`Lấy danh sách tài liệu cho tenant ${tenantId}`);

    // Parse tags nếu có
    let tags: string[] | undefined;
    if (queryDto.tags) {
      tags = queryDto.tags.split(',').map(tag => tag.trim());
    }

    const result = await this.documentRepository.findAll(tenantId, {
      page: queryDto.page,
      limit: queryDto.limit,
      search: queryDto.search,
      documentType: queryDto.documentType,
      folderId: queryDto.folderId,
      isActive: queryDto.isActive,
      isPublic: queryDto.isPublic,
      processingStatus: queryDto.processingStatus,
      tags,
      sortBy: queryDto.sortBy,
      sortDirection: queryDto.sortDirection,
    });

    return {
      ...result,
      items: result.items.map(document => this.mapToResponseDto(document)),
    };
  }

  /**
   * Tìm kiếm full-text
   * @param tenantId ID tenant
   * @param searchDto Tham số tìm kiếm
   * @param userId ID người tìm kiếm
   * @returns Kết quả tìm kiếm đã phân trang
   */
  async searchFullText(
    tenantId: number,
    searchDto: DocumentSearchDto,
    userId: number,
  ): Promise<PaginatedResult<DocumentResponseDto>> {
    this.logger.log(`Tìm kiếm full-text: "${searchDto.searchText}" cho tenant ${tenantId}`);

    const startTime = Date.now();

    try {
      const result = await this.documentRepository.searchFullText(tenantId, searchDto.searchText, {
        page: searchDto.page,
        limit: searchDto.limit,
        documentType: searchDto.documentType,
        folderId: searchDto.folderId,
      });

      const responseTime = Date.now() - startTime;

      // Log tìm kiếm
      await this.logAccess(tenantId, null, userId, 'search', {
        success: true,
        queryText: searchDto.searchText,
        responseTimeMs: responseTime,
        metadata: {
          resultsCount: result.meta.totalItems,
          page: searchDto.page,
          limit: searchDto.limit,
        },
      });

      return {
        ...result,
        items: result.items.map(document => this.mapToResponseDto(document)),
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      // Log lỗi tìm kiếm
      await this.logAccess(tenantId, null, userId, 'search', {
        success: false,
        queryText: searchDto.searchText,
        responseTimeMs: responseTime,
        errorMessage: error.message,
      });

      throw error;
    }
  }

  /**
   * Lấy tài liệu theo ID
   * @param tenantId ID tenant
   * @param id ID tài liệu
   * @param userId ID người truy cập (để log)
   * @returns Tài liệu hoặc null
   */
  async findById(
    tenantId: number,
    id: number,
    userId?: number,
  ): Promise<DocumentResponseDto | null> {
    this.logger.log(`Lấy tài liệu ID: ${id} cho tenant ${tenantId}`);

    const document = await this.documentRepository.findById(tenantId, id);
    if (!document) {
      return null;
    }

    // Log truy cập nếu có userId
    if (userId) {
      await this.logAccess(tenantId, id, userId, 'view', {
        success: true,
        metadata: { documentTitle: document.title },
      });
    }

    return this.mapToResponseDto(document);
  }

  /**
   * Cập nhật tài liệu
   * @param tenantId ID tenant
   * @param id ID tài liệu
   * @param updateDto Dữ liệu cập nhật
   * @param updatedBy ID người cập nhật
   * @returns Tài liệu đã cập nhật hoặc null
   */
  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateDocumentDto,
    updatedBy: number,
  ): Promise<DocumentResponseDto | null> {
    this.logger.log(`Cập nhật tài liệu ID: ${id} cho tenant ${tenantId}`);

    // Kiểm tra tài liệu tồn tại
    const existingDocument = await this.documentRepository.findById(tenantId, id);
    if (!existingDocument) {
      return null;
    }

    // Kiểm tra thư mục mới nếu có thay đổi
    if (updateDto.folderId !== undefined && updateDto.folderId !== existingDocument.folderId) {
      if (updateDto.folderId) {
        const folder = await this.documentFolderRepository.findById(tenantId, updateDto.folderId);
        if (!folder) {
          throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_NOT_FOUND);
        }
        if (!folder.isActive) {
          throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_INACTIVE);
        }
      }
    }

    const updatedDocument = await this.documentRepository.update(tenantId, id, {
      ...updateDto,
      updatedBy,
    });

    if (!updatedDocument) {
      return null;
    }

    // Log cập nhật
    await this.logAccess(tenantId, id, updatedBy, 'edit', {
      success: true,
      metadata: { action: 'update_document', changes: Object.keys(updateDto) },
    });

    this.logger.log(`Đã cập nhật tài liệu ID: ${id}`);
    return this.mapToResponseDto(updatedDocument);
  }

  /**
   * Xóa tài liệu (soft delete)
   * @param tenantId ID tenant
   * @param id ID tài liệu
   * @param deletedBy ID người xóa
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, id: number, deletedBy: number): Promise<boolean> {
    this.logger.log(`Xóa tài liệu ID: ${id} cho tenant ${tenantId}`);

    // Kiểm tra tài liệu tồn tại
    const document = await this.documentRepository.findById(tenantId, id);
    if (!document) {
      return false;
    }

    const success = await this.documentRepository.delete(tenantId, id);
    
    if (success) {
      // Log xóa
      await this.logAccess(tenantId, id, deletedBy, 'delete', {
        success: true,
        metadata: { action: 'delete_document', documentTitle: document.title },
      });

      this.logger.log(`Đã xóa tài liệu ID: ${id}`);
    }
    
    return success;
  }

  /**
   * Log truy cập tài liệu
   * @param tenantId ID tenant
   * @param documentId ID tài liệu (null cho tìm kiếm tổng quát)
   * @param userId ID người dùng
   * @param accessType Loại truy cập
   * @param options Tùy chọn log
   */
  private async logAccess(
    tenantId: number,
    documentId: number | null,
    userId: number,
    accessType: 'view' | 'download' | 'search' | 'rag_query' | 'edit' | 'delete',
    options: {
      success?: boolean;
      queryText?: string;
      queryResults?: Record<string, any>;
      queryConfidence?: number;
      responseTimeMs?: number;
      errorMessage?: string;
      metadata?: Record<string, any>;
    } = {},
  ): Promise<void> {
    try {
      await this.documentAccessLogRepository.create(tenantId, {
        documentId,
        userId,
        accessType,
        success: options.success ?? true,
        queryText: options.queryText,
        queryResults: options.queryResults,
        queryConfidence: options.queryConfidence,
        responseTimeMs: options.responseTimeMs,
        errorMessage: options.errorMessage,
        metadata: options.metadata,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi log truy cập: ${error.message}`);
      // Không throw error để không ảnh hưởng đến business logic chính
    }
  }

  /**
   * Chuyển đổi entity thành response DTO
   * @param document Entity tài liệu
   * @returns Response DTO
   */
  private mapToResponseDto(document: Document): DocumentResponseDto {
    return {
      id: document.id,
      title: document.title,
      description: document.description,
      documentType: document.documentType,
      fileName: document.fileName,
      fileSize: document.fileSize,
      mimeType: document.mimeType,
      folderId: document.folderId,
      s3Key: document.s3Key,
      s3Bucket: document.s3Bucket,
      openaiFileId: document.openaiFileId,
      openaiVectorStoreId: document.openaiVectorStoreId,
      processingStatus: document.processingStatus,
      processingError: document.processingError,
      lastProcessedAt: document.lastProcessedAt,
      retryCount: document.retryCount,
      contentHash: document.contentHash,
      pageCount: document.pageCount,
      wordCount: document.wordCount,
      language: document.language,
      isActive: document.isActive,
      isPublic: document.isPublic,
      version: document.version,
      tags: document.tags,
      metadata: document.metadata,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
      createdBy: document.createdBy,
      updatedBy: document.updatedBy,
      tenantId: document.tenantId,
      // Computed properties
      fileSizeMB: document.fileSizeMB,
      isProcessed: document.isProcessed,
      isProcessingFailed: document.isProcessingFailed,
      canRetry: document.canRetry,
      fileExtension: document.fileExtension,
      isPDF: document.isPDF,
      isWordDocument: document.isWordDocument,
      isTextFile: document.isTextFile,
    };
  }
}
