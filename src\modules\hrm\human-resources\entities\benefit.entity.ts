import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing employee benefits packages
 */
@Entity('benefits')
export class Benefit {
  /**
   * Unique identifier for the benefit
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Benefit code/identifier
   */
  @Column({ name: 'benefit_code', type: 'varchar', length: 100, nullable: false, unique: true })
  benefitCode: string;

  /**
   * Benefit name/title
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Benefit description
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Benefit category (health, insurance, retirement, wellness, etc.)
   */
  @Column({ type: 'varchar', length: 100, nullable: false })
  category: string;

  /**
   * Benefit type (medical, dental, vision, life_insurance, etc.)
   */
  @Column({ type: 'varchar', length: 100, nullable: false })
  type: string;

  /**
   * Benefit provider/vendor
   */
  @Column({ type: 'varchar', length: 255, nullable: true })
  provider: string | null;

  /**
   * Monthly cost for employer
   */
  @Column({ name: 'employer_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  employerCost: number | null;

  /**
   * Monthly cost for employee
   */
  @Column({ name: 'employee_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  employeeCost: number | null;

  /**
   * Currency for costs
   */
  @Column({ type: 'varchar', length: 10, default: 'VND' })
  currency: string;

  /**
   * Coverage amount/limit
   */
  @Column({ name: 'coverage_amount', type: 'decimal', precision: 15, scale: 2, nullable: true })
  coverageAmount: number | null;

  /**
   * Deductible amount
   */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  deductible: number | null;

  /**
   * Co-payment percentage
   */
  @Column({ name: 'copay_percentage', type: 'decimal', precision: 5, scale: 2, nullable: true })
  copayPercentage: number | null;

  /**
   * Benefit eligibility criteria
   */
  @Column({ name: 'eligibility_criteria', type: 'text', nullable: true })
  eligibilityCriteria: string | null;

  /**
   * Waiting period before benefit becomes active (in days)
   */
  @Column({ name: 'waiting_period_days', type: 'integer', nullable: true })
  waitingPeriodDays: number | null;

  /**
   * Whether benefit covers dependents
   */
  @Column({ name: 'covers_dependents', type: 'boolean', default: false })
  coversDependents: boolean;

  /**
   * Maximum age for dependent coverage
   */
  @Column({ name: 'dependent_age_limit', type: 'integer', nullable: true })
  dependentAgeLimit: number | null;

  /**
   * Benefit enrollment period start
   */
  @Column({ name: 'enrollment_start', type: 'date', nullable: true })
  enrollmentStart: Date | null;

  /**
   * Benefit enrollment period end
   */
  @Column({ name: 'enrollment_end', type: 'date', nullable: true })
  enrollmentEnd: Date | null;

  /**
   * Benefit effective start date
   */
  @Column({ name: 'effective_start', type: 'date', nullable: true })
  effectiveStart: Date | null;

  /**
   * Benefit effective end date
   */
  @Column({ name: 'effective_end', type: 'date', nullable: true })
  effectiveEnd: Date | null;

  /**
   * Benefit status (active, inactive, suspended)
   */
  @Column({ type: 'varchar', length: 50, nullable: false, default: 'active' })
  status: string;

  /**
   * Whether benefit is mandatory for all employees
   */
  @Column({ name: 'is_mandatory', type: 'boolean', default: false })
  isMandatory: boolean;

  /**
   * Whether employees can opt out of this benefit
   */
  @Column({ name: 'can_opt_out', type: 'boolean', default: true })
  canOptOut: boolean;

  /**
   * Benefit terms and conditions
   */
  @Column({ name: 'terms_conditions', type: 'text', nullable: true })
  termsConditions: string | null;

  /**
   * Contact information for benefit provider
   */
  @Column({ name: 'provider_contact', type: 'text', nullable: true })
  providerContact: string | null;

  /**
   * Benefit documentation/forms
   */
  @Column({ type: 'text', nullable: true })
  documentation: string | null;

  /**
   * Additional benefit details
   */
  @Column({ name: 'additional_details', type: 'text', nullable: true })
  additionalDetails: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: false })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   * REQUIRED for tenant isolation
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
