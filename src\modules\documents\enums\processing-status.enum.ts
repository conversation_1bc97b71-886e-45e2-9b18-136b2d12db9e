/**
 * Enum for OpenAI processing status
 */
export enum ProcessingStatus {
  PENDING = 'pending',
  UPLOADING_TO_OPENAI = 'uploading_to_openai',
  ADDING_TO_VECTOR_STORE = 'adding_to_vector_store',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRY = 'retry',
}

/**
 * Human-readable labels for processing status
 */
export const ProcessingStatusLabels = {
  [ProcessingStatus.PENDING]: 'Đang chờ xử lý',
  [ProcessingStatus.UPLOADING_TO_OPENAI]: 'Đang tải lên OpenAI',
  [ProcessingStatus.ADDING_TO_VECTOR_STORE]: 'Đang thêm vào Vector Store',
  [ProcessingStatus.COMPLETED]: 'Hoàn thành',
  [ProcessingStatus.FAILED]: 'Thất bại',
  [ProcessingStatus.RETRY]: 'Đang thử lại',
};

/**
 * Processing status that can be retried
 */
export const RetryableProcessingStatuses = [
  ProcessingStatus.FAILED,
  ProcessingStatus.RETRY,
];

/**
 * Final processing status that cannot be changed
 */
export const FinalProcessingStatuses = [
  ProcessingStatus.COMPLETED,
];
