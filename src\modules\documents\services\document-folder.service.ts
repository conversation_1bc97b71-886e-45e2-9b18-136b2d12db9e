import { Injectable, Logger } from '@nestjs/common';
import { DocumentFolderRepository } from '../repositories/document-folder.repository';
import {
  CreateDocumentFolderDto,
  UpdateDocumentFolderDto,
  DocumentFolderQueryDto,
  DocumentFolderResponseDto,
  DocumentFolderWithStatsResponseDto,
} from '../dto/document-folder';
import { DocumentFolder } from '../entities/document-folder.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { DOCUMENTS_ERROR_CODES } from '../errors';

/**
 * Service xử lý logic nghiệp vụ cho thư mục tài liệu
 */
@Injectable()
export class DocumentFolderService {
  private readonly logger = new Logger(DocumentFolderService.name);

  constructor(
    private readonly documentFolderRepository: DocumentFolderRepository,
  ) {}

  /**
   * Tạo thư mục mới
   * @param tenantId ID tenant
   * @param createDto Dữ liệu tạo thư mục
   * @param createdBy ID người tạo
   * @returns Thư mục đã tạo
   */
  async create(
    tenantId: number,
    createDto: CreateDocumentFolderDto,
    createdBy: number,
  ): Promise<DocumentFolderResponseDto> {
    this.logger.log(`Tạo thư mục mới: ${createDto.name} cho tenant ${tenantId}`);

    // Kiểm tra thư mục cha nếu có
    if (createDto.parentId) {
      const parentFolder = await this.documentFolderRepository.findById(tenantId, createDto.parentId);
      if (!parentFolder) {
        throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_NOT_FOUND);
      }
      if (!parentFolder.isActive) {
        throw new AppException(DOCUMENTS_ERROR_CODES.PARENT_FOLDER_INACTIVE);
      }
    }

    // Tính toán path và level
    const { path, level } = await this.calculatePathAndLevel(tenantId, createDto.parentId);

    // Tạo thư mục
    const folderData: Partial<DocumentFolder> = {
      ...createDto,
      path,
      level,
      createdBy,
      updatedBy: createdBy,
    };

    const folder = await this.documentFolderRepository.create(tenantId, folderData);
    
    this.logger.log(`Đã tạo thư mục ID: ${folder.id}`);
    return this.mapToResponseDto(folder);
  }

  /**
   * Lấy danh sách thư mục với phân trang
   * @param tenantId ID tenant
   * @param queryDto Tham số truy vấn
   * @returns Danh sách thư mục đã phân trang
   */
  async findAll(
    tenantId: number,
    queryDto: DocumentFolderQueryDto,
  ): Promise<PaginatedResult<DocumentFolderResponseDto>> {
    this.logger.log(`Lấy danh sách thư mục cho tenant ${tenantId}`);

    const result = await this.documentFolderRepository.findAll(tenantId, {
      page: queryDto.page,
      limit: queryDto.limit,
      search: queryDto.search,
      parentId: queryDto.parentId,
      isActive: queryDto.isActive,
      sortBy: queryDto.sortBy,
      sortDirection: queryDto.sortDirection,
    });

    return {
      ...result,
      items: result.items.map(folder => this.mapToResponseDto(folder)),
    };
  }

  /**
   * Lấy thư mục theo ID
   * @param tenantId ID tenant
   * @param id ID thư mục
   * @returns Thư mục hoặc null
   */
  async findById(tenantId: number, id: number): Promise<DocumentFolderResponseDto | null> {
    this.logger.log(`Lấy thư mục ID: ${id} cho tenant ${tenantId}`);

    const folder = await this.documentFolderRepository.findById(tenantId, id);
    if (!folder) {
      return null;
    }

    return this.mapToResponseDto(folder);
  }

  /**
   * Lấy thư mục gốc
   * @param tenantId ID tenant
   * @returns Danh sách thư mục gốc
   */
  async findRootFolders(tenantId: number): Promise<DocumentFolderResponseDto[]> {
    this.logger.log(`Lấy thư mục gốc cho tenant ${tenantId}`);

    const folders = await this.documentFolderRepository.findRootFolders(tenantId);
    return folders.map(folder => this.mapToResponseDto(folder));
  }

  /**
   * Lấy thư mục con
   * @param tenantId ID tenant
   * @param parentId ID thư mục cha
   * @returns Danh sách thư mục con
   */
  async findChildren(tenantId: number, parentId: number): Promise<DocumentFolderResponseDto[]> {
    this.logger.log(`Lấy thư mục con của ${parentId} cho tenant ${tenantId}`);

    // Kiểm tra thư mục cha tồn tại
    const parentFolder = await this.documentFolderRepository.findById(tenantId, parentId);
    if (!parentFolder) {
      throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_NOT_FOUND);
    }

    const folders = await this.documentFolderRepository.findChildren(tenantId, parentId);
    return folders.map(folder => this.mapToResponseDto(folder));
  }

  /**
   * Cập nhật thư mục
   * @param tenantId ID tenant
   * @param id ID thư mục
   * @param updateDto Dữ liệu cập nhật
   * @param updatedBy ID người cập nhật
   * @returns Thư mục đã cập nhật hoặc null
   */
  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateDocumentFolderDto,
    updatedBy: number,
  ): Promise<DocumentFolderResponseDto | null> {
    this.logger.log(`Cập nhật thư mục ID: ${id} cho tenant ${tenantId}`);

    // Kiểm tra thư mục tồn tại
    const existingFolder = await this.documentFolderRepository.findById(tenantId, id);
    if (!existingFolder) {
      return null;
    }

    // Kiểm tra thư mục cha mới nếu có thay đổi
    if (updateDto.parentId !== undefined && updateDto.parentId !== existingFolder.parentId) {
      if (updateDto.parentId) {
        const newParentFolder = await this.documentFolderRepository.findById(tenantId, updateDto.parentId);
        if (!newParentFolder) {
          throw new AppException(DOCUMENTS_ERROR_CODES.PARENT_FOLDER_NOT_FOUND);
        }
        if (!newParentFolder.isActive) {
          throw new AppException(DOCUMENTS_ERROR_CODES.PARENT_FOLDER_INACTIVE);
        }
        
        // Kiểm tra không tạo vòng lặp (folder không thể là cha của chính nó)
        if (updateDto.parentId === id) {
          throw new AppException(DOCUMENTS_ERROR_CODES.CIRCULAR_REFERENCE);
        }
      }

      // Tính toán lại path và level nếu thay đổi parent
      const { path, level } = await this.calculatePathAndLevel(tenantId, updateDto.parentId);
      updateDto = { ...updateDto, path, level } as any;
    }

    const updatedFolder = await this.documentFolderRepository.update(tenantId, id, {
      ...updateDto,
      updatedBy,
    });

    if (!updatedFolder) {
      return null;
    }

    this.logger.log(`Đã cập nhật thư mục ID: ${id}`);
    return this.mapToResponseDto(updatedFolder);
  }

  /**
   * Xóa thư mục (soft delete)
   * @param tenantId ID tenant
   * @param id ID thư mục
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    this.logger.log(`Xóa thư mục ID: ${id} cho tenant ${tenantId}`);

    // Kiểm tra thư mục tồn tại
    const folder = await this.documentFolderRepository.findById(tenantId, id);
    if (!folder) {
      return false;
    }

    // Kiểm tra có thư mục con không
    const childrenCount = await this.documentFolderRepository.countChildren(tenantId, id);
    if (childrenCount > 0) {
      throw new AppException(DOCUMENTS_ERROR_CODES.FOLDER_HAS_CHILDREN);
    }

    // TODO: Kiểm tra có tài liệu trong thư mục không
    // const documentsCount = await this.documentRepository.countByFolder(tenantId, id);
    // if (documentsCount > 0) {
    //   throw new AppException('FOLDER_HAS_DOCUMENTS', 'Không thể xóa thư mục có chứa tài liệu');
    // }

    const success = await this.documentFolderRepository.delete(tenantId, id);
    
    if (success) {
      this.logger.log(`Đã xóa thư mục ID: ${id}`);
    }
    
    return success;
  }

  /**
   * Tính toán path và level cho thư mục
   * @param tenantId ID tenant
   * @param parentId ID thư mục cha
   * @returns Path và level
   */
  private async calculatePathAndLevel(
    tenantId: number,
    parentId?: number,
  ): Promise<{ path: string; level: number }> {
    if (!parentId) {
      // Thư mục gốc
      return { path: '/', level: 0 };
    }

    const parentFolder = await this.documentFolderRepository.findById(tenantId, parentId);
    if (!parentFolder) {
      throw new AppException(DOCUMENTS_ERROR_CODES.PARENT_FOLDER_NOT_FOUND);
    }

    const path = `${parentFolder.path}${parentFolder.name}/`;
    const level = parentFolder.level + 1;

    return { path, level };
  }

  /**
   * Chuyển đổi entity thành response DTO
   * @param folder Entity thư mục
   * @returns Response DTO
   */
  private mapToResponseDto(folder: DocumentFolder): DocumentFolderResponseDto {
    return {
      id: folder.id,
      name: folder.name,
      description: folder.description,
      parentId: folder.parentId,
      path: folder.path,
      level: folder.level,
      sortOrder: folder.sortOrder,
      isActive: folder.isActive,
      metadata: folder.metadata,
      createdAt: folder.createdAt,
      updatedAt: folder.updatedAt,
      createdBy: folder.createdBy,
      updatedBy: folder.updatedBy,
      tenantId: folder.tenantId,
      pathArray: folder.pathArray,
      isRoot: folder.isRoot,
    };
  }
}
