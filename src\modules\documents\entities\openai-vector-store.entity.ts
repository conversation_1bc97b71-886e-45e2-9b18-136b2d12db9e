import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';

@Entity('openai_vector_stores')
@Index(['tenantId'], { unique: true })
@Index(['vectorStoreId'], { unique: true })
@Index(['status'])
@Index(['expiresAt'])
export class OpenAIVectorStore {
  /**
   * Khóa chính
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID OpenAI Vector Store từ OpenAI API
   */
  @Column({ name: 'vector_store_id', type: 'varchar', length: 100, unique: true })
  vectorStoreId: string;

  /**
   * Tên vector store
   */
  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  /**
   * Mô tả vector store
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Số lượng file trong vector store
   */
  @Column({ name: 'file_count', type: 'integer', default: 0 })
  fileCount: number;

  /**
   * Tổng dung lượng sử dụng tính bằng bytes
   */
  @Column({ name: 'usage_bytes', type: 'bigint', default: 0 })
  usageBytes: number;

  /**
   * Trạng thái vector store
   */
  @Column({ name: 'status', type: 'varchar', length: 50, default: 'active' })
  status: 'active' | 'expired' | 'deleting';

  /**
   * Thời điểm hết hạn OpenAI vector store
   */
  @Column({ name: 'expires_at', type: 'bigint', nullable: true })
  expiresAt: number | null;

  /**
   * Thời điểm đồng bộ cuối với OpenAI
   */
  @Column({ name: 'last_synced_at', type: 'bigint', nullable: true })
  lastSyncedAt: number | null;

  /**
   * Metadata OpenAI
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Thời điểm tạo (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID tenant để phân tách dữ liệu multi-tenant
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // THUỘC TÍNH TÍNH TOÁN
  // =====================================================

  /**
   * Kiểm tra vector store đã hết hạn chưa
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return Date.now() > this.expiresAt;
  }

  /**
   * Kiểm tra vector store có đang hoạt động và chưa hết hạn không
   */
  get isActive(): boolean {
    return this.status === 'active' && !this.isExpired;
  }

  /**
   * Lấy dung lượng sử dụng tính bằng MB
   */
  get usageMB(): number {
    return Math.round(this.usageBytes / (1024 * 1024) * 100) / 100;
  }

  /**
   * Lấy số ngày còn lại đến khi hết hạn
   */
  get daysUntilExpiration(): number | null {
    if (!this.expiresAt) return null;
    const msUntilExpiration = this.expiresAt - Date.now();
    return Math.ceil(msUntilExpiration / (1000 * 60 * 60 * 24));
  }
}
