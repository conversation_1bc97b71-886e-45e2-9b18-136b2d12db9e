import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Patch,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { CalendarService } from '../services/calendar.service';
import {
  CalendarEventDto,
  CreateCalendarEventDto,
  UpdateCalendarEventDto,
} from '../dto/calendar-event.dto';
import { CalendarQueryDto } from '../dto/calendar-query.dto';

/**
 * Controller cho Calendar
 */
@ApiTags(SWAGGER_API_TAG.CALENDAR)
@ApiExtraModels(ApiResponseDto, CalendarEventDto)
@Controller('api/calendar')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class CalendarController {
  constructor(private readonly calendarService: CalendarService) {}

  /**
   * Lấy tất cả sự kiện lịch trong khoảng thời gian
   */
  @Get('events')
  @ApiOperation({ summary: 'Lấy tất cả sự kiện lịch trong khoảng thời gian' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách sự kiện lịch',
    schema: ApiResponseDto.getPaginatedSchema(CalendarEventDto),
  })
  async getEvents(
    @Query() query: CalendarQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<CalendarEventDto>>> {
    const events = await this.calendarService.getCalendarEvents(Number(user.tenantId), query, user.id);
    return ApiResponseDto.success(events);
  }

  /**
   * Lấy chi tiết sự kiện lịch
   */
  @Get('events/:id')
  @ApiOperation({ summary: 'Lấy chi tiết sự kiện lịch' })
  @ApiParam({ name: 'id', description: 'ID của sự kiện lịch', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết sự kiện lịch',
    schema: ApiResponseDto.getSchema(CalendarEventDto),
  })
  async getEvent(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CalendarEventDto>> {
    const event = await this.calendarService.getCalendarEvent(Number(user.tenantId), id, user.id);
    return ApiResponseDto.success(event);
  }

  /**
   * Tạo sự kiện lịch mới
   */
  @Post('events')
  @ApiOperation({ summary: 'Tạo sự kiện lịch mới' })
  @ApiResponse({
    status: 201,
    description: 'Sự kiện lịch đã được tạo',
    schema: ApiResponseDto.getSchema(CalendarEventDto),
  })
  async createEvent(
    @Body() createDto: CreateCalendarEventDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CalendarEventDto>> {
    const event = await this.calendarService.createCalendarEvent(
      Number(user.tenantId),
      createDto,
      user.id,
    );
    return ApiResponseDto.created(event);
  }

  /**
   * Cập nhật sự kiện lịch
   */
  @Patch('events/:id')
  @ApiOperation({ summary: 'Cập nhật sự kiện lịch' })
  @ApiParam({ name: 'id', description: 'ID của sự kiện lịch', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Sự kiện lịch đã được cập nhật',
    schema: ApiResponseDto.getSchema(CalendarEventDto),
  })
  async updateEvent(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCalendarEventDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CalendarEventDto>> {
    const event = await this.calendarService.updateCalendarEvent(
      Number(user.tenantId),
      id,
      updateDto,
      user.id,
    );
    return ApiResponseDto.success(event);
  }

  /**
   * Xóa sự kiện lịch
   */
  @Delete('events/:id')
  @ApiOperation({ summary: 'Xóa sự kiện lịch' })
  @ApiParam({ name: 'id', description: 'ID của sự kiện lịch', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Sự kiện lịch đã được xóa',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async deleteEvent(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.calendarService.deleteCalendarEvent(Number(user.tenantId), id, user.id);
    return ApiResponseDto.deleted(result);
  }
}
