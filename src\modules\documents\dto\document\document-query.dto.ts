import { IsOptional, IsString, IsNumber, IsBoolean, IsIn, IsEnum, IsArray } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentType, ProcessingStatus } from '../../enums';

/**
 * DTO để truy vấn danh sách tài liệu
 */
export class DocumentQueryDto {
  /**
   * Số trang
   */
  @ApiPropertyOptional({
    description: 'Số trang',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  /**
   * Số lượng items per page
   */
  @ApiPropertyOptional({
    description: 'Số lượng items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  /**
   * Từ khóa tìm kiếm
   */
  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm trong tiêu đề, mô tả và tên file',
    example: 'chính sách',
  })
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Loại tài liệu
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại tài liệu',
    enum: DocumentType,
    example: DocumentType.POLICY,
  })
  @IsOptional()
  @IsEnum(DocumentType)
  documentType?: DocumentType;

  /**
   * ID thư mục
   */
  @ApiPropertyOptional({
    description: 'ID thư mục (null để lấy tài liệu không có thư mục)',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  folderId?: number;

  /**
   * Trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái hoạt động',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isActive?: boolean;

  /**
   * Tài liệu công khai
   */
  @ApiPropertyOptional({
    description: 'Lọc theo tài liệu công khai',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isPublic?: boolean;

  /**
   * Trạng thái xử lý
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái xử lý OpenAI',
    enum: ProcessingStatus,
    example: ProcessingStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ProcessingStatus)
  processingStatus?: ProcessingStatus;

  /**
   * Thẻ tags
   */
  @ApiPropertyOptional({
    description: 'Lọc theo thẻ tags (có thể nhiều tags, cách nhau bằng dấu phẩy)',
    example: 'hr,policy',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  /**
   * Ngày tạo từ
   */
  @ApiPropertyOptional({
    description: 'Lọc tài liệu tạo từ ngày này (timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  createdFrom?: number;

  /**
   * Ngày tạo đến
   */
  @ApiPropertyOptional({
    description: 'Lọc tài liệu tạo đến ngày này (timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  createdTo?: number;

  /**
   * Kích thước file tối thiểu (bytes)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo kích thước file tối thiểu (bytes)',
    example: 1024,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minFileSize?: number;

  /**
   * Kích thước file tối đa (bytes)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo kích thước file tối đa (bytes)',
    example: 10485760,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  maxFileSize?: number;

  /**
   * Trường sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Trường để sắp xếp',
    example: 'createdAt',
    enum: ['title', 'createdAt', 'updatedAt', 'fileSize', 'documentType'],
    default: 'createdAt',
  })
  @IsOptional()
  @IsString()
  @IsIn(['title', 'createdAt', 'updatedAt', 'fileSize', 'documentType'])
  sortBy?: string = 'createdAt';

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortDirection?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * DTO để tìm kiếm full-text
 */
export class DocumentSearchDto {
  /**
   * Văn bản tìm kiếm
   */
  @ApiPropertyOptional({
    description: 'Văn bản tìm kiếm full-text',
    example: 'chính sách làm việc từ xa',
  })
  @IsString()
  searchText: string;

  /**
   * Số trang
   */
  @ApiPropertyOptional({
    description: 'Số trang',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  /**
   * Số lượng items per page
   */
  @ApiPropertyOptional({
    description: 'Số lượng items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  /**
   * Loại tài liệu
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại tài liệu',
    enum: DocumentType,
    example: DocumentType.POLICY,
  })
  @IsOptional()
  @IsEnum(DocumentType)
  documentType?: DocumentType;

  /**
   * ID thư mục
   */
  @ApiPropertyOptional({
    description: 'ID thư mục',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  folderId?: number;
}
