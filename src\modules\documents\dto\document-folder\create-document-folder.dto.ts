import { IsString, IsOptional, IsN<PERSON>ber, IsBoolean, IsObject, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO để tạo thư mục tài liệu mới
 */
export class CreateDocumentFolderDto {
  /**
   * Tên thư mục
   */
  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Chính sách nhân sự',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  /**
   * <PERSON>ô tả thư mục
   */
  @ApiPropertyOptional({
    description: '<PERSON>ô tả thư mục',
    example: 'Th<PERSON> mục chứa các chính sách liên quan đến nhân sự',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID thư mục cha
   */
  @ApiPropertyOptional({
    description: 'ID thư mục cha (null nếu là thư mục gốc)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  parentId?: number;

  /**
   * Thứ tự sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Thứ tự sắp xếp trong cùng cấp độ',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung của thư mục',
    example: { color: '#blue', icon: 'folder' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
