import { PartialType } from '@nestjs/swagger';
import { CreateDocumentFolderDto } from './create-document-folder.dto';
import { IsOptional, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO để cập nhật thư mục tài liệu
 */
export class UpdateDocumentFolderDto extends PartialType(CreateDocumentFolderDto) {
  /**
   * Trạng thái hoạt động của thư mục
   */
  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của thư mục',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
