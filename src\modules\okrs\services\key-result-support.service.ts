import { Injectable, Logger } from '@nestjs/common';
import { KeyResultSupportRepository } from '../repositories/key-result-support.repository';
import { KeyResultRepository } from '../repositories/key-result.repository';
import {
  CreateKeyResultSupportDto,
  KeyResultSupportResponseDto,
} from '../dto/key-result-support';
import { KeyResultSupport } from '../entities/key-result-supports';
import { AppException } from '@/common';
import { OKRS_ERROR_CODES } from '../errors/okrs-error.code';

/**
 * Service for key result support relationships
 */
@Injectable()
export class KeyResultSupportService {
  private readonly logger = new Logger(KeyResultSupportService.name);

  constructor(
    private readonly keyResultSupportRepository: KeyResultSupportRepository,
    private readonly keyResultRepository: KeyResultRepository,
  ) {}

  /**
   * Add supporting key results to a parent key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId Parent key result ID
   * @param dto Create key result support DTO
   * @returns List of key result support responses
   */
  async addSupports(
    tenantId: number,
    parentId: number,
    dto: CreateKeyResultSupportDto,
  ): Promise<KeyResultSupportResponseDto[]> {
    // Validate parent key result exists
    const parentKeyResult = await this.keyResultRepository.findById(
      tenantId,
      parentId,
    );
    if (!parentKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${parentId}`,
      );
    }

    // Validate child key results exist and check for cycles
    for (const childId of dto.childIds) {
      // Check if child key result exists
      const childKeyResult = await this.keyResultRepository.findById(
        tenantId,
        childId,
      );
      if (!childKeyResult) {
        throw new AppException(
          OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
          `Không tìm thấy kết quả chính hỗ trợ với ID ${childId}`,
        );
      }

      // Check if child is the same as parent
      if (childId === parentId) {
        throw new AppException(
          OKRS_ERROR_CODES.KEY_RESULT_SUPPORT_SELF_REFERENCE,
          'Kết quả chính không thể hỗ trợ chính nó',
        );
      }

      // Check if support relationship already exists
      const exists = await this.keyResultSupportRepository.exists(
        parentId,
        childId,
      );
      if (exists) {
        throw new AppException(
          OKRS_ERROR_CODES.KEY_RESULT_SUPPORT_ALREADY_EXISTS,
          `Kết quả chính ${childId} đã hỗ trợ kết quả chính ${parentId}`,
        );
      }

      // Check if adding this support would create a cycle
      const wouldCreateCycle =
        await this.keyResultSupportRepository.wouldCreateCycle(
          parentId,
          childId,
        );
      if (wouldCreateCycle) {
        throw new AppException(
          OKRS_ERROR_CODES.KEY_RESULT_SUPPORT_CYCLE,
          `Thêm kết quả chính ${childId} làm hỗ trợ sẽ tạo ra chu trình phụ thuộc`,
        );
      }
    }

    // Create support relationships
    const supports = await this.keyResultSupportRepository.createMany(
      parentId,
      dto.childIds,
    );

    // Map to response DTOs
    const responses: KeyResultSupportResponseDto[] = [];
    for (const support of supports) {
      const childKeyResult = await this.keyResultRepository.findById(
        tenantId,
        support.childId,
      );
      responses.push(this.mapToResponseDto(support, childKeyResult));
    }

    return responses;
  }

  /**
   * Get supporting key results for a parent key result
   * @param parentId Parent key result ID
   * @returns List of key result support responses
   */
  async getSupports(
    tenantId: number,
    parentId: number,
  ): Promise<KeyResultSupportResponseDto[]> {
    // Validate parent key result exists
    const parentKeyResult = await this.keyResultRepository.findById(
      tenantId,
      parentId,
    );
    if (!parentKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${parentId}`,
      );
    }

    // Get support relationships
    const supports =
      await this.keyResultSupportRepository.findByParentId(parentId);

    // Map to response DTOs
    const responses: KeyResultSupportResponseDto[] = [];
    for (const support of supports) {
      const childKeyResult = await this.keyResultRepository.findById(
        tenantId,
        support.childId,
      );
      if (childKeyResult) {
        responses.push(this.mapToResponseDto(support, childKeyResult));
      }
    }

    return responses;
  }

  /**
   * Remove a supporting key result from a parent key result
   * @param parentId Parent key result ID
   * @param childId Child key result ID
   * @returns True if removed
   */
  async removeSupport(
    tenantId: number,
    parentId: number,
     childId: number
  ): Promise<boolean> {
    // Validate parent key result exists
    const parentKeyResult = await this.keyResultRepository.findById(tenantId, parentId);
    if (!parentKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${parentId}`,
      );
    }

    // Validate child key result exists
    const childKeyResult = await this.keyResultRepository.findById(tenantId, childId);
    if (!childKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính hỗ trợ với ID ${childId}`,
      );
    }

    // Check if support relationship exists
    const exists = await this.keyResultSupportRepository.exists(
      parentId,
      childId,
    );
    if (!exists) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_SUPPORT_NOT_FOUND,
        `Kết quả chính ${childId} không hỗ trợ kết quả chính ${parentId}`,
      );
    }

    // Remove support relationship
    const removed = await this.keyResultSupportRepository.delete(
      parentId,
      childId,
    );

    return removed;
  }

  /**
   * Map key result support entity to response DTO
   * @param support Key result support entity
   * @param childKeyResult Child key result entity
   * @returns Key result support response DTO
   */
  private mapToResponseDto(
    support: KeyResultSupport,
    childKeyResult: any,
  ): KeyResultSupportResponseDto {
    const response = new KeyResultSupportResponseDto();

    response.parentId = support.parentId;
    response.childId = support.childId;
    response.childKeyResult = childKeyResult
      ? {
          id: childKeyResult.id,
          title: childKeyResult.title,
          description: childKeyResult.description,
          targetValue: childKeyResult.targetValue,
          currentValue: childKeyResult.currentValue,
          startValue: childKeyResult.startValue,
          unit: childKeyResult.unit,
          format: childKeyResult.format,
          progress: childKeyResult.progress,
          status: childKeyResult.status,
          objectiveId: childKeyResult.objectiveId,
          measurementMethod: childKeyResult.measurementMethod,
          weight: childKeyResult.weight,
          checkInFrequency: childKeyResult.checkInFrequency,
          createdAt: childKeyResult.createdAt,
          updatedAt: childKeyResult.updatedAt,
        }
      : undefined;

    return response;
  }
}
